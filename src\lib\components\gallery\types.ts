
export interface PhotoSwipeItem {
    src: string
    w: number
    h: number
    title?: string
    msrc?: string
    el: Element
    pid?: string
}

export interface PhotoSwipeOptions {
    galleryUID: string | null
    getThumbBoundsFn: (index: number) => { x: number; y: number; w: number }
    index?: number
    galleryPIDs?: boolean
    showAnimationDuration?: number
}

export interface HashParams {
    gid?: number
    pid?: string
    [key: string]: string | number | undefined
}